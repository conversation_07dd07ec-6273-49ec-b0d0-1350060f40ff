<?php
/**
 * Middleware de Segurança
 * Santa Casa da Misericórdia da Covilhã - Painel Administrativo
 */

class SecurityMiddleware {
    private $auditLog;
    
    public function __construct() {
        $this->auditLog = new AuditLog();
    }
    
    /**
     * Verificar proteção CSRF
     */
    public function checkCSRF() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $token = $_POST['csrf_token'] ?? $_SERVER['HTTP_X_CSRF_TOKEN'] ?? '';
            
            if (!verifyCSRFToken($token)) {
                $this->logSecurityEvent('csrf_token_invalid');
                $this->sendSecurityResponse('Token CSRF inválido', 403);
            }
        }
    }
    
    /**
     * Verificar rate limiting
     */
    public function checkRateLimit($action = 'general', $maxAttempts = 60, $timeWindow = 3600) {
        $ip = $this->getClientIP();

        // Verificar se o IP está na whitelist (IPs autenticados)
        if ($this->isIPWhitelisted($ip)) {
            return; // IP autenticado - sem rate limiting
        }

        $key = "rate_limit_{$action}_{$ip}";

        // Usar sessão para armazenar contadores (em produção usar Redis/Memcached)
        if (!isset($_SESSION['rate_limits'])) {
            $_SESSION['rate_limits'] = [];
        }

        $now = time();

        // Limpar entradas expiradas
        foreach ($_SESSION['rate_limits'] as $k => $data) {
            if ($data['expires'] < $now) {
                unset($_SESSION['rate_limits'][$k]);
            }
        }

        // Verificar limite atual
        if (isset($_SESSION['rate_limits'][$key])) {
            $data = $_SESSION['rate_limits'][$key];

            if ($data['count'] >= $maxAttempts) {
                $this->logSecurityEvent('rate_limit_exceeded', [
                    'action' => $action,
                    'ip' => $ip,
                    'attempts' => $data['count']
                ]);
                $this->sendSecurityResponse('Muitas tentativas. Tente novamente mais tarde.', 429);
            }

            $_SESSION['rate_limits'][$key]['count']++;
        } else {
            $_SESSION['rate_limits'][$key] = [
                'count' => 1,
                'expires' => $now + $timeWindow
            ];
        }
    }
    
    /**
     * Validar e sanitizar input
     */
    public function sanitizeInput($data) {
        if (is_array($data)) {
            return array_map([$this, 'sanitizeInput'], $data);
        }
        
        // Remover caracteres de controle
        $data = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $data);
        
        // Sanitizar HTML
        $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        
        // Remover espaços em branco desnecessários
        $data = trim($data);
        
        return $data;
    }
    
    /**
     * Verificar injeção SQL básica
     */
    public function checkSQLInjection($input) {
        $patterns = [
            '/(\bunion\b.*\bselect\b)/i',
            '/(\bselect\b.*\bfrom\b)/i',
            '/(\binsert\b.*\binto\b)/i',
            '/(\bupdate\b.*\bset\b)/i',
            '/(\bdelete\b.*\bfrom\b)/i',
            '/(\bdrop\b.*\btable\b)/i',
            '/(\bexec\b|\bexecute\b)/i',
            '/(\bscript\b.*\>)/i'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $input)) {
                $this->logSecurityEvent('sql_injection_attempt', ['input' => substr($input, 0, 100)]);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Verificar XSS básico
     */
    public function checkXSS($input) {
        $patterns = [
            '/<script[^>]*>.*?<\/script>/is',
            '/<iframe[^>]*>.*?<\/iframe>/is',
            '/javascript:/i',
            '/on\w+\s*=/i',
            '/<object[^>]*>.*?<\/object>/is',
            '/<embed[^>]*>/i'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $input)) {
                $this->logSecurityEvent('xss_attempt', ['input' => substr($input, 0, 100)]);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Verificar headers de segurança
     */
    public function checkSecurityHeaders() {
        // Verificar se HTTPS está sendo usado
        if (!$this->isHTTPS() && defined('FORCE_HTTPS') && FORCE_HTTPS) {
            $this->redirectToHTTPS();
        }
        
        // Verificar User-Agent suspeito
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        if ($this->isSuspiciousUserAgent($userAgent)) {
            $this->logSecurityEvent('suspicious_user_agent', ['user_agent' => $userAgent]);
        }
        
        // Verificar Referer suspeito
        $referer = $_SERVER['HTTP_REFERER'] ?? '';
        if ($referer && !$this->isValidReferer($referer)) {
            $this->logSecurityEvent('suspicious_referer', ['referer' => $referer]);
        }
    }
    
    /**
     * Verificar se está usando HTTPS
     */
    private function isHTTPS() {
        return (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ||
               $_SERVER['SERVER_PORT'] == 443 ||
               (!empty($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https');
    }
    
    /**
     * Redirecionar para HTTPS
     */
    private function redirectToHTTPS() {
        $redirectURL = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        header("Location: $redirectURL", true, 301);
        exit();
    }
    
    /**
     * Verificar User-Agent suspeito
     */
    private function isSuspiciousUserAgent($userAgent) {
        $suspiciousPatterns = [
            '/bot/i',
            '/crawler/i',
            '/spider/i',
            '/scraper/i',
            '/curl/i',
            '/wget/i',
            '/python/i',
            '/perl/i'
        ];
        
        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $userAgent)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Verificar Referer válido
     */
    private function isValidReferer($referer) {
        $parsedReferer = parse_url($referer);
        $currentHost = $_SERVER['HTTP_HOST'];
        
        return isset($parsedReferer['host']) && $parsedReferer['host'] === $currentHost;
    }

    /**
     * Verificar se o IP está na whitelist (IPs autenticados)
     */
    private function isIPWhitelisted($ip) {
        // Verificar se existe uma sessão ativa para este IP
        if (isset($_SESSION['user_id']) && isset($_SESSION['authenticated_ip'])) {
            if ($_SESSION['authenticated_ip'] === $ip) {
                return true;
            }
        }

        // Verificar na base de dados se existe uma sessão ativa para este IP
        try {
            $db = new Database();

            $activeSession = $db->fetch(
                "SELECT COUNT(*) as count FROM admin_sessions
                 WHERE ip_address = ? AND last_activity > ?",
                [$ip, time() - 3600] // Sessões ativas nas últimas 1 hora
            );

            return $activeSession['count'] > 0;

        } catch (Exception $e) {
            // Em caso de erro na base de dados, não bloquear
            error_log("Error checking IP whitelist: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Adicionar IP à whitelist após login bem-sucedido
     */
    public function whitelistIP($ip = null) {
        if ($ip === null) {
            $ip = $this->getClientIP();
        }

        // Marcar IP como autenticado na sessão
        $_SESSION['authenticated_ip'] = $ip;

        // Limpar rate limits para este IP
        $this->clearRateLimitsForIP($ip);

        $this->logSecurityEvent('ip_whitelisted', ['ip' => $ip]);
    }

    /**
     * Remover IP da whitelist (logout)
     */
    public function removeIPFromWhitelist($ip = null) {
        if ($ip === null) {
            $ip = $this->getClientIP();
        }

        // Remover da sessão
        if (isset($_SESSION['authenticated_ip']) && $_SESSION['authenticated_ip'] === $ip) {
            unset($_SESSION['authenticated_ip']);
        }

        $this->logSecurityEvent('ip_removed_from_whitelist', ['ip' => $ip]);
    }

    /**
     * Limpar rate limits para um IP específico
     */
    private function clearRateLimitsForIP($ip) {
        if (!isset($_SESSION['rate_limits'])) {
            return;
        }

        $prefix = "_" . $ip;
        foreach ($_SESSION['rate_limits'] as $key => $data) {
            if (strpos($key, $prefix) !== false) {
                unset($_SESSION['rate_limits'][$key]);
            }
        }
    }

    /**
     * Limpar rate limits (para debugging/emergência)
     */
    public function clearRateLimits($action = null, $ip = null) {
        if (!isset($_SESSION['rate_limits'])) {
            return;
        }

        if ($action && $ip) {
            // Limpar rate limit específico
            $key = "rate_limit_{$action}_{$ip}";
            unset($_SESSION['rate_limits'][$key]);
        } elseif ($action) {
            // Limpar todos os rate limits para uma ação específica
            $prefix = "rate_limit_{$action}_";
            foreach ($_SESSION['rate_limits'] as $key => $data) {
                if (strpos($key, $prefix) === 0) {
                    unset($_SESSION['rate_limits'][$key]);
                }
            }
        } else {
            // Limpar todos os rate limits
            $_SESSION['rate_limits'] = [];
        }
    }

    /**
     * Obter IP real do cliente
     */
    private function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];

        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];

                // Se for uma lista de IPs, pegar o primeiro
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }

                // Validar IP (permitir IPs privados para desenvolvimento local)
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }
    
    /**
     * Registar evento de segurança
     */
    private function logSecurityEvent($event, $data = []) {
        $logData = array_merge([
            'event' => $event,
            'ip' => $this->getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
            'timestamp' => date('Y-m-d H:i:s')
        ], $data);
        
        // Log no sistema de auditoria
        $userId = $_SESSION['user_id'] ?? null;
        $this->auditLog->log($userId, 'security_event', null, null, null, $logData);
        
        // Log adicional para análise
        error_log("SECURITY EVENT: " . json_encode($logData));
    }
    
    /**
     * Enviar resposta de segurança
     */
    private function sendSecurityResponse($message, $statusCode = 403) {
        http_response_code($statusCode);
        
        if ($this->isAjaxRequest()) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => $message,
                'error_code' => $statusCode
            ]);
        } else {
            // Redirecionar para página de erro ou mostrar mensagem
            header('Content-Type: text/html; charset=UTF-8');
            echo "<!DOCTYPE html>
            <html>
            <head>
                <meta charset='UTF-8'>
                <title>Acesso Negado</title>
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; margin-top: 100px; }
                    .error { color: #d32f2f; }
                </style>
            </head>
            <body>
                <h1 class='error'>Acesso Negado</h1>
                <p>{$message}</p>
                <p><a href='/admin'>Voltar ao painel</a></p>
            </body>
            </html>";
        }
        
        exit();
    }
    
    /**
     * Verificar se é uma requisição AJAX
     */
    private function isAjaxRequest() {
        return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
    
    /**
     * Middleware principal - executar todas as verificações
     */
    public function handle() {
        // Verificar headers de segurança
        $this->checkSecurityHeaders();

        // NOTE: Rate limiting removido do handle() geral
        // Rate limiting deve ser aplicado apenas em ações específicas como login
        // Usar $security->checkRateLimit('action', maxAttempts, timeWindow) conforme necessário

        // Verificar CSRF para requisições POST
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->checkCSRF();
        }

        // Sanitizar input
        $_GET = $this->sanitizeInput($_GET);
        $_POST = $this->sanitizeInput($_POST);

        // Verificar tentativas de injeção
        $allInput = array_merge($_GET, $_POST);
        foreach ($allInput as $key => $value) {
            if (is_string($value)) {
                if ($this->checkSQLInjection($value) || $this->checkXSS($value)) {
                    $this->sendSecurityResponse('Input suspeito detectado', 400);
                }
            }
        }
    }
}
