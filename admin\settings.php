<?php
/**
 * Configurações do Sistema
 * Santa Casa da Misericórdia da Covilhã
 */

// Inicializar configuração
require_once 'config/config.php';

// Inicializar middleware de segurança
$security = new SecurityMiddleware();
$security->handle();

// Inicializar autenticação
$auth = new Auth();

// Verificar se o utilizador está autenticado
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit();
}

// Obter utilizador atual
$currentUser = $auth->getCurrentUser();

// Verificar permissões
if (!$auth->hasPermission('settings.view')) {
    header('Location: unauthorized.php');
    exit();
}

$pageTitle = 'Configurações do Sistema';
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="utf-8" />
    <title><?php echo $pageTitle; ?> - <?php echo APP_NAME; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
    <meta content="Configurações do sistema da Santa Casa da Misericórdia da Covilhã" name="description" />
    <meta content="Santa Casa da Misericórdia da Covilhã" name="author" />

    <!-- App favicon -->
    <link rel="shortcut icon" href="assets/images/favicon.ico">

    <!-- Theme Config Js -->
    <script src="assets/js/config.js"></script>

    <!-- App css -->
    <link href="assets/css/app.min.css" rel="stylesheet" type="text/css" id="app-style" />

    <!-- Icons css -->
    <link href="assets/css/icons.min.css" rel="stylesheet" type="text/css" />

    <!-- Custom Admin css -->
    <link href="assets/css/admin.css" rel="stylesheet" type="text/css" />
</head>

<body>
    <!-- Begin page -->
    <div class="wrapper">

        <!-- ========== Topbar Start ========== -->
        <div class="navbar-custom">
            <div class="topbar container-fluid">
                <div class="d-flex align-items-center gap-1">

                    <!-- Topbar Brand Logo -->
                    <div class="logo-topbar">
                        <!-- Logo light -->
                        <a href="index.php" class="logo-light">
                            <span class="logo-lg">
                                <img src="assets/images/logo-custom.svg" alt="Santa Casa da Misericórdia da Covilhã" height="30">
                            </span>
                            <span class="logo-sm">
                                <img src="assets/images/logo-sm-custom.svg" alt="Santa Casa" height="30">
                            </span>
                        </a>

                        <!-- Logo Dark -->
                        <a href="index.php" class="logo-dark">
                            <span class="logo-lg">
                                <img src="assets/images/logo-custom.svg" alt="Santa Casa da Misericórdia da Covilhã" height="30">
                            </span>
                            <span class="logo-sm">
                                <img src="assets/images/logo-sm-custom.svg" alt="Santa Casa" height="30">
                            </span>
                        </a>
                    </div>

                    <!-- Sidebar Menu Toggle Button -->
                    <button class="button-toggle-menu">
                        <i class="ri-menu-line"></i>
                    </button>

                    <!-- Horizontal Menu Toggle Button -->
                    <button class="navbar-toggle" data-bs-toggle="collapse" data-bs-target="#topnav-menu-content">
                        <div class="lines">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </button>

                    <!-- Topbar Search Form -->
                    <div class="app-search d-none d-lg-block">
                        <form>
                            <div class="input-group">
                                <input type="search" class="form-control" placeholder="Pesquisar...">
                                <span class="ri-search-line search-icon text-muted"></span>
                            </div>
                        </form>
                    </div>
                </div>

                <ul class="topbar-menu d-flex align-items-center gap-3">
                    <li class="dropdown d-lg-none">
                        <a class="nav-link dropdown-toggle arrow-none" data-bs-toggle="dropdown" href="#" role="button"
                            aria-haspopup="false" aria-expanded="false">
                            <i class="ri-search-line fs-22"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-animated dropdown-lg p-0">
                            <form class="p-3">
                                <input type="search" class="form-control" placeholder="Pesquisar..."
                                    aria-label="Pesquisar">
                            </form>
                        </div>
                    </li>

                    <li class="dropdown notification-list">
                        <a class="nav-link dropdown-toggle arrow-none" data-bs-toggle="dropdown" href="#" role="button"
                            aria-haspopup="false" aria-expanded="false">
                            <i class="ri-notification-3-line fs-22"></i>
                            <span class="noti-icon-badge badge text-bg-pink">0</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end dropdown-menu-animated dropdown-lg py-0">
                            <div class="p-2 border-top-0 border-start-0 border-end-0 border-dashed border">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h6 class="m-0 fs-16 fw-semibold">Notificações</h6>
                                    </div>
                                    <div class="col-auto">
                                        <a href="javascript: void(0);" class="text-dark text-decoration-underline">
                                            <small>Limpar Todas</small>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="px-1" style="max-height: 300px;" data-simplebar>
                                <h5 class="text-muted fs-13 fw-normal mt-2">Hoje</h5>
                                <p class="text-muted text-center py-3">Nenhuma notificação</p>
                            </div>
                            <a href="javascript:void(0);" class="dropdown-item text-center text-primary notify-item border-top py-2">
                                Ver Todas
                            </a>
                        </div>
                    </li>

                    <li class="dropdown">
                        <a class="nav-link dropdown-toggle arrow-none nav-user" data-bs-toggle="dropdown" href="#" role="button"
                            aria-haspopup="false" aria-expanded="false">
                            <span class="account-user-avatar">
                                <div style="width: 32px; height: 32px; border-radius: 50%; background: linear-gradient(135deg, #2c5aa0, #1e3d6f); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 0.8rem;">
                                    <?php echo strtoupper(substr($currentUser['first_name'], 0, 1) . substr($currentUser['last_name'], 0, 1)); ?>
                                </div>
                            </span>
                            <span class="d-lg-block d-none">
                                <h5 class="my-0 fw-normal"><?php echo htmlspecialchars($currentUser['first_name'] . ' ' . $currentUser['last_name']); ?> <i class="ri-arrow-down-s-line d-none d-sm-inline-block align-middle"></i></h5>
                            </span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end dropdown-menu-animated profile-dropdown">
                            <!-- item-->
                            <div class=" dropdown-header noti-title">
                                <h6 class="text-overflow m-0">Bem-vindo!</h6>
                            </div>

                            <!-- item-->
                            <a href="profile.php" class="dropdown-item notify-item">
                                <i class="ri-account-circle-line fs-16 align-middle"></i>
                                <span>Meu Perfil</span>
                            </a>

                            <!-- item-->
                            <a href="change-password.php" class="dropdown-item notify-item">
                                <i class="ri-settings-4-line fs-16 align-middle"></i>
                                <span>Alterar Password</span>
                            </a>

                            <!-- item-->
                            <a href="logout.php" class="dropdown-item notify-item">
                                <i class="ri-logout-box-line fs-16 align-middle"></i>
                                <span>Terminar Sessão</span>
                            </a>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        <!-- ========== Topbar End ========== -->

        <!-- ========== Left Sidebar Start ========== -->
        <div class="leftside-menu">

            <!-- Brand Logo Light -->
            <a href="index.php" class="logo logo-light">
                <span class="logo-lg">
                    <img src="assets/images/logo-custom.svg" alt="Santa Casa da Misericórdia da Covilhã" height="35">
                </span>
                <span class="logo-sm">
                    <img src="assets/images/logo-sm-custom.svg" alt="Santa Casa" height="35">
                </span>
            </a>

            <!-- Brand Logo Dark -->
            <a href="index.php" class="logo logo-dark">
                <span class="logo-lg">
                    <img src="assets/images/logo-custom.svg" alt="Santa Casa da Misericórdia da Covilhã" height="35">
                </span>
                <span class="logo-sm">
                    <img src="assets/images/logo-sm-custom.svg" alt="Santa Casa" height="35">
                </span>
            </a>

            <!-- Sidebar -left -->
            <div class="h-100" id="leftside-menu-container" data-simplebar>
                <!--- Sidemenu -->
                <ul class="side-nav">

                    <li class="side-nav-title">Principal</li>

                    <li class="side-nav-item">
                        <a href="index.php" class="side-nav-link">
                            <i class="ri-dashboard-3-line"></i>
                            <span> Painel de Controlo </span>
                        </a>
                    </li>

                    <?php if ($auth->hasPermission('users.view') || $auth->hasPermission('content.view')): ?>
                    <li class="side-nav-title">Gestão</li>

                    <?php if ($auth->hasPermission('users.view')): ?>
                    <li class="side-nav-item">
                        <a href="users.php" class="side-nav-link">
                            <i class="ri-group-2-line"></i>
                            <span> Utilizadores </span>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php if ($auth->hasPermission('content.view')): ?>
                    <li class="side-nav-item">
                        <a href="content.php" class="side-nav-link">
                            <i class="ri-file-text-line"></i>
                            <span> Conteúdos </span>
                        </a>
                    </li>
                    <?php endif; ?>
                    <?php endif; ?>

                    <?php if ($auth->hasPermission('audit.view') || $auth->hasPermission('settings.view')): ?>
                    <li class="side-nav-title">Sistema</li>

                    <?php if ($auth->hasPermission('audit.view')): ?>
                    <li class="side-nav-item">
                        <a href="audit.php" class="side-nav-link">
                            <i class="ri-search-eye-line"></i>
                            <span> Auditoria </span>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php if ($auth->hasPermission('settings.view')): ?>
                    <li class="side-nav-item">
                        <a href="settings.php" class="side-nav-link active">
                            <i class="ri-settings-4-line"></i>
                            <span> Configurações </span>
                        </a>
                    </li>
                    <?php endif; ?>
                    <?php endif; ?>

                </ul>
                <!--- End Sidemenu -->

                <div class="clearfix"></div>
            </div>
        </div>
        <!-- ========== Left Sidebar End ========== -->

        <!-- ============================================================== -->
        <!-- Start Page Content here -->
        <!-- ============================================================== -->

        <div class="content-page">
            <div class="content">

                <!-- Start Content-->
                <div class="container-fluid">

                    <!-- start page title -->
                    <div class="row">
                        <div class="col-12">
                            <div class="page-title-box">
                                <div class="page-title-right">
                                    <ol class="breadcrumb m-0">
                                        <li class="breadcrumb-item"><a href="javascript: void(0);">Santa Casa</a></li>
                                        <li class="breadcrumb-item"><a href="javascript: void(0);">Sistema</a></li>
                                        <li class="breadcrumb-item active">Configurações</li>
                                    </ol>
                                </div>
                                <h4 class="page-title">Configurações do Sistema</h4>
                            </div>
                        </div>
                    </div>
                    <!-- end page title -->

                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <h4 class="header-title">Configurações Gerais</h4>
                                    <p class="text-muted fs-14">Configure as definições gerais do sistema.</p>

                                    <form>
                                        <div class="row">
                                            <div class="col-lg-6">
                                                <div class="mb-3">
                                                    <label for="app-name" class="form-label">Nome da Aplicação</label>
                                                    <input type="text" id="app-name" class="form-control" value="<?php echo APP_NAME; ?>" readonly>
                                                </div>
                                            </div>
                                            <div class="col-lg-6">
                                                <div class="mb-3">
                                                    <label for="app-version" class="form-label">Versão</label>
                                                    <input type="text" id="app-version" class="form-control" value="<?php echo APP_VERSION; ?>" readonly>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-lg-6">
                                                <div class="mb-3">
                                                    <label for="php-version" class="form-label">Versão PHP</label>
                                                    <input type="text" id="php-version" class="form-control" value="<?php echo PHP_VERSION; ?>" readonly>
                                                </div>
                                            </div>
                                            <div class="col-lg-6">
                                                <div class="mb-3">
                                                    <label for="server-software" class="form-label">Servidor</label>
                                                    <input type="text" id="server-software" class="form-control" value="<?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'N/A'; ?>" readonly>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-12">
                                                <div class="mb-3">
                                                    <label class="form-label">Configurações de Segurança</label>
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" id="enable-2fa" checked>
                                                        <label class="form-check-label" for="enable-2fa">
                                                            Ativar Autenticação de Dois Fatores (2FA)
                                                        </label>
                                                    </div>
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" id="enable-audit" checked>
                                                        <label class="form-check-label" for="enable-audit">
                                                            Ativar Log de Auditoria
                                                        </label>
                                                    </div>
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" id="force-https" checked>
                                                        <label class="form-check-label" for="force-https">
                                                            Forçar HTTPS
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="text-end">
                                            <button type="submit" class="btn btn-success">
                                                <i class="ri-save-line me-1"></i> Guardar Configurações
                                            </button>
                                        </div>
                                    </form>
                                </div> <!-- end card-body -->
                            </div> <!-- end card -->
                        </div> <!-- end col -->
                    </div>
                    <!-- end row -->

                    <div class="row">
                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-body">
                                    <h4 class="header-title">Backup do Sistema</h4>
                                    <p class="text-muted fs-14">Faça backup da base de dados e ficheiros do sistema.</p>

                                    <div class="d-grid gap-2">
                                        <button type="button" class="btn btn-primary">
                                            <i class="ri-download-cloud-line me-1"></i> Fazer Backup da Base de Dados
                                        </button>
                                        <button type="button" class="btn btn-info">
                                            <i class="ri-folder-download-line me-1"></i> Fazer Backup dos Ficheiros
                                        </button>
                                        <button type="button" class="btn btn-success">
                                            <i class="ri-archive-line me-1"></i> Backup Completo
                                        </button>
                                    </div>

                                    <hr>

                                    <h5>Último Backup</h5>
                                    <p class="text-muted mb-0">
                                        <i class="ri-time-line me-1"></i>
                                        <?php echo date('d/m/Y H:i'); ?> (Automático)
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-body">
                                    <h4 class="header-title">Manutenção do Sistema</h4>
                                    <p class="text-muted fs-14">Ferramentas de manutenção e limpeza do sistema.</p>

                                    <div class="d-grid gap-2">
                                        <button type="button" class="btn btn-warning">
                                            <i class="ri-broom-line me-1"></i> Limpar Cache
                                        </button>
                                        <button type="button" class="btn btn-info">
                                            <i class="ri-refresh-line me-1"></i> Otimizar Base de Dados
                                        </button>
                                        <button type="button" class="btn btn-danger">
                                            <i class="ri-delete-bin-line me-1"></i> Limpar Logs Antigos
                                        </button>
                                    </div>

                                    <hr>

                                    <h5>Estado do Sistema</h5>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span>Estado:</span>
                                        <span class="badge bg-success-subtle text-success">Online</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- end row -->

                </div> <!-- container -->

            </div> <!-- content -->

            <!-- Footer Start -->
            <footer class="footer">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-md-6">
                            <script>document.write(new Date().getFullYear())</script> © Santa Casa da Misericórdia da Covilhã
                        </div>
                        <div class="col-md-6">
                            <div class="text-md-end footer-links d-none d-md-block">
                                <a href="javascript: void(0);">Sobre</a>
                                <a href="javascript: void(0);">Suporte</a>
                                <a href="javascript: void(0);">Contacto</a>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
            <!-- end Footer -->

        </div>

        <!-- ============================================================== -->
        <!-- End Page content -->
        <!-- ============================================================== -->

    </div>
    <!-- END wrapper -->

    <!-- Vendor js -->
    <script src="assets/js/vendor.min.js"></script>

    <!-- App js -->
    <script src="assets/js/app.min.js"></script>

    <!-- Custom Admin js -->
    <script src="assets/js/admin.js"></script>

</body>
</html>
