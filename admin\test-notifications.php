<?php
/**
 * Teste do Sistema de Notificações
 * Santa Casa da Misericórdia da Covilhã
 */

// Inicializar configuração
require_once 'config/config.php';

// Inicializar middleware de segurança
$security = new SecurityMiddleware();
$security->handle();

// Inicializar autenticação
$auth = new Auth();

// Verificar se o utilizador está autenticado
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit();
}

$pageTitle = 'Teste de Notificações';
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="utf-8" />
    <title><?php echo $pageTitle; ?> - <?php echo APP_NAME; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
    <meta content="Teste do sistema de notificações" name="description" />
    <meta content="Santa Casa da Misericórdia da Covilhã" name="author" />

    <!-- App favicon -->
    <link rel="shortcut icon" href="assets/images/favicon.ico">

    <!-- Theme Config Js -->
    <script src="assets/js/config.js"></script>

    <!-- App css -->
    <link href="assets/css/app.min.css" rel="stylesheet" type="text/css" id="app-style" />

    <!-- Icons css -->
    <link href="assets/css/icons.min.css" rel="stylesheet" type="text/css" />
    
    <!-- Custom Admin css -->
    <link href="assets/css/admin.css" rel="stylesheet" type="text/css" />
</head>

<body>
    <div class="wrapper">
        <div class="content-page">
            <div class="content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div class="page-title-box">
                                <h4 class="page-title">Teste de Notificações</h4>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Testar Sistema de Notificações</h5>
                                    <p class="card-text">Clique nos botões abaixo para testar diferentes tipos de notificações:</p>
                                    
                                    <div class="d-flex gap-2 flex-wrap">
                                        <button type="button" class="btn btn-success" onclick="testSuccessNotification()">
                                            <i class="ri-check-line"></i> Sucesso
                                        </button>
                                        <button type="button" class="btn btn-danger" onclick="testErrorNotification()">
                                            <i class="ri-error-warning-line"></i> Erro
                                        </button>
                                        <button type="button" class="btn btn-warning" onclick="testWarningNotification()">
                                            <i class="ri-alert-line"></i> Aviso
                                        </button>
                                        <button type="button" class="btn btn-info" onclick="testInfoNotification()">
                                            <i class="ri-information-line"></i> Informação
                                        </button>
                                        <button type="button" class="btn btn-primary" onclick="testConfirmation()">
                                            <i class="ri-question-line"></i> Confirmação
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vendor js -->
    <script src="assets/js/vendor.min.js"></script>

    <!-- App js -->
    <script src="assets/js/app.min.js"></script>

    <!-- Custom Admin js -->
    <script src="assets/js/admin.js"></script>

    <script>
        // Initialize admin panel
        document.addEventListener('DOMContentLoaded', function() {
            AdminPanel.init();
        });

        function testSuccessNotification() {
            AdminPanel.showNotification('Operação realizada com sucesso!', 'success');
        }

        function testErrorNotification() {
            AdminPanel.showNotification('Ocorreu um erro durante a operação.', 'danger');
        }

        function testWarningNotification() {
            AdminPanel.showNotification('Atenção: Esta ação pode ter consequências.', 'warning');
        }

        function testInfoNotification() {
            AdminPanel.showNotification('Esta é uma mensagem informativa.', 'info');
        }

        function testConfirmation() {
            AdminPanel.showConfirmation(
                'Tem a certeza que deseja continuar com esta ação?',
                function() {
                    AdminPanel.showNotification('Ação confirmada!', 'success');
                },
                function() {
                    AdminPanel.showNotification('Ação cancelada.', 'info');
                }
            );
        }
    </script>
</body>
</html>
