<?php
/**
 * Página de Alteração de Password
 * Santa Casa da Misericórdia da Covilhã
 */

// Inicializar configuração
require_once 'config/config.php';

// Inicializar middleware de segurança
$security = new SecurityMiddleware();
$security->handle();

// Inicializar autenticação
$auth = new Auth();

// Verificar se o utilizador está autenticado
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit();
}

// Obter utilizador atual
$currentUser = $auth->getCurrentUser();

// Inicializar base de dados
$db = new Database();

$error = '';
$success = '';

// Processar formulário de alteração de password
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['change_password'])) {
    // Verificar CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Token de segurança inválido. Tente novamente.';
    } else {
        $currentPassword = $_POST['current_password'] ?? '';
        $newPassword = $_POST['new_password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
    
    // Validações
    if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
        $error = 'Todos os campos são obrigatórios.';
    } elseif ($newPassword !== $confirmPassword) {
        $error = 'A nova password e a confirmação não coincidem.';
    } elseif (strlen($newPassword) < 8) {
        $error = 'A nova password deve ter pelo menos 8 caracteres.';
    } elseif (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/', $newPassword)) {
        $error = 'A nova password deve conter pelo menos: 1 letra minúscula, 1 maiúscula, 1 número e 1 símbolo.';
    } else {
        // Verificar password atual
        if (!password_verify($currentPassword, $currentUser['password_hash'])) {
            $error = 'Password atual incorreta.';
        } else {
            try {
                // Gerar hash da nova password
                $newPasswordHash = password_hash($newPassword, PASSWORD_DEFAULT);
                
                // Atualizar password
                $db->execute(
                    "UPDATE admin_users SET password_hash = ?, updated_at = NOW() WHERE id = ?",
                    [$newPasswordHash, $currentUser['id']]
                );
                
                // Log da ação
                $auditLog = new AuditLog();
                $auditLog->log(
                    $currentUser['id'],
                    'password_changed',
                    'admin_users',
                    $currentUser['id'],
                    null,
                    ['password_changed' => true]
                );
                
                $success = 'Password alterada com sucesso!';
                
            } catch (Exception $e) {
                error_log("Erro ao alterar password: " . $e->getMessage());
                $error = 'Erro interno. Tente novamente.';
            }
        }
    }
}

$pageTitle = 'Alterar Password';
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="utf-8" />
    <title><?php echo $pageTitle; ?> - <?php echo APP_NAME; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
    <meta content="Painel administrativo da Santa Casa da Misericórdia da Covilhã" name="description" />
    <meta content="Santa Casa da Misericórdia da Covilhã" name="author" />

    <!-- App favicon -->
    <link rel="shortcut icon" href="assets/images/favicon.ico">

    <!-- Theme Config Js -->
    <script src="assets/js/config.js"></script>

    <!-- App css -->
    <link href="assets/css/app.min.css" rel="stylesheet" type="text/css" id="app-style" />

    <!-- Icons css -->
    <link href="assets/css/icons.min.css" rel="stylesheet" type="text/css" />

    <!-- Custom Admin css -->
    <link href="assets/css/admin.css" rel="stylesheet" type="text/css" />
</head>

<body>
    <!-- Begin page -->
    <div class="wrapper">

        <!-- ========== Topbar Start ========== -->
        <div class="navbar-custom">
            <div class="topbar container-fluid">
                <div class="d-flex align-items-center gap-1">

                    <!-- Topbar Brand Logo -->
                    <div class="logo-topbar">
                        <!-- Logo light -->
                        <a href="index.php" class="logo-light">
                            <span class="logo-lg">
                                <img src="assets/images/logos/logotipo-branco.svg" alt="Santa Casa da Misericórdia da Covilhã" height="30">
                            </span>
                            <span class="logo-sm">
                                <img src="assets/images/logos/brasao-branco.svg" alt="Santa Casa" height="30">
                            </span>
                        </a>

                        <!-- Logo Dark -->
                        <a href="index.php" class="logo-dark">
                            <span class="logo-lg">
                                <img src="assets/images/logos/logotipo-preto.svg" alt="Santa Casa da Misericórdia da Covilhã" height="30">
                            </span>
                            <span class="logo-sm">
                                <img src="assets/images/logos/brasao-preto.svg" alt="Santa Casa" height="30">
                            </span>
                        </a>
                    </div>

                    <!-- Sidebar Menu Toggle Button -->
                    <button class="button-toggle-menu">
                        <i class="ri-menu-line"></i>
                    </button>

                    <!-- Horizontal Menu Toggle Button -->
                    <button class="navbar-toggle" data-bs-toggle="collapse" data-bs-target="#topnav-menu-content">
                        <div class="lines">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </button>

                    <!-- Topbar Search Form -->
                    <div class="app-search d-none d-lg-block">
                        <form>
                            <div class="input-group">
                                <input type="search" class="form-control" placeholder="Pesquisar...">
                                <span class="ri-search-line search-icon text-muted"></span>
                            </div>
                        </form>
                    </div>
                </div>

                <ul class="topbar-menu d-flex align-items-center gap-3">
                    <li class="dropdown d-lg-none">
                        <a class="nav-link dropdown-toggle arrow-none" data-bs-toggle="dropdown" href="#" role="button"
                            aria-haspopup="false" aria-expanded="false">
                            <i class="ri-search-line fs-22"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-animated dropdown-lg p-0">
                            <form class="p-3">
                                <input type="search" class="form-control" placeholder="Pesquisar..."
                                    aria-label="Pesquisar">
                            </form>
                        </div>
                    </li>

                    <li class="dropdown notification-list">
                        <a class="nav-link dropdown-toggle arrow-none" data-bs-toggle="dropdown" href="#" role="button"
                            aria-haspopup="false" aria-expanded="false">
                            <i class="ri-notification-3-line fs-22"></i>
                            <span class="noti-icon-badge badge text-bg-pink">0</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end dropdown-menu-animated dropdown-lg py-0">
                            <div class="p-2 border-top-0 border-start-0 border-end-0 border-dashed border">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h6 class="m-0 fs-16 fw-semibold">Notificações</h6>
                                    </div>
                                    <div class="col-auto">
                                        <a href="javascript: void(0);" class="text-dark text-decoration-underline">
                                            <small>Limpar Todas</small>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="px-1" style="max-height: 300px;" data-simplebar>
                                <div class="text-center py-4">
                                    <i class="ri-notification-off-line fs-48 text-muted"></i>
                                    <p class="text-muted mt-2">Nenhuma notificação.</p>
                                </div>
                            </div>
                        </div>
                    </li>

                    <li class="dropdown">
                        <a class="nav-link dropdown-toggle arrow-none nav-user" data-bs-toggle="dropdown" href="#" role="button"
                            aria-haspopup="false" aria-expanded="false">
                            <span class="account-user-avatar">
                                <div style="width: 32px; height: 32px; border-radius: 50%; background: linear-gradient(135deg, #2c5aa0, #1e3d6f); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 0.8rem;">
                                    <?php echo strtoupper(substr($currentUser['first_name'], 0, 1) . substr($currentUser['last_name'], 0, 1)); ?>
                                </div>
                            </span>
                            <span class="d-lg-block d-none">
                                <h5 class="my-0 fw-normal"><?php echo htmlspecialchars($currentUser['first_name'] . ' ' . $currentUser['last_name']); ?> <i class="ri-arrow-down-s-line d-none d-sm-inline-block align-middle"></i></h5>
                            </span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end dropdown-menu-animated profile-dropdown">
                            <!-- item-->
                            <div class=" dropdown-header noti-title">
                                <h6 class="text-overflow m-0">Bem-vindo!</h6>
                            </div>

                            <!-- item-->
                            <a href="profile.php" class="dropdown-item notify-item">
                                <i class="ri-account-circle-line fs-16 align-middle"></i>
                                <span>Meu Perfil</span>
                            </a>

                            <!-- item-->
                            <a href="change-password.php" class="dropdown-item notify-item">
                                <i class="ri-settings-4-line fs-16 align-middle"></i>
                                <span>Alterar Password</span>
                            </a>

                            <!-- item-->
                            <a href="logout.php" class="dropdown-item notify-item">
                                <i class="ri-logout-box-line fs-16 align-middle"></i>
                                <span>Terminar Sessão</span>
                            </a>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        <!-- ========== Topbar End ========== -->

        <!-- ========== Left Sidebar Start ========== -->
        <div class="leftside-menu">

            <!-- Brand Logo Light -->
            <a href="index.php" class="logo logo-light">
                <span class="logo-lg">
                    <img src="assets/images/logos/logotipo-branco.svg" alt="Santa Casa da Misericórdia da Covilhã" height="35">
                </span>
                <span class="logo-sm">
                    <img src="assets/images/logos/brasao-branco.svg" alt="Santa Casa" height="35">
                </span>
            </a>

            <!-- Brand Logo Dark -->
            <a href="index.php" class="logo logo-dark">
                <span class="logo-lg">
                    <img src="assets/images/logos/logotipo-preto.svg" alt="Santa Casa da Misericórdia da Covilhã" height="35">
                </span>
                <span class="logo-sm">
                    <img src="assets/images/logos/brasao-preto.svg" alt="Santa Casa" height="35">
                </span>
            </a>

            <!-- Sidebar -left -->
            <div class="h-100" id="leftside-menu-container" data-simplebar>
                <!--- Sidemenu -->
                <ul class="side-nav">

                    <li class="side-nav-title">Principal</li>

                    <li class="side-nav-item">
                        <a href="index.php" class="side-nav-link">
                            <i class="ri-dashboard-3-line"></i>
                            <span> Painel de Controlo </span>
                        </a>
                    </li>

                    <?php if ($auth->hasPermission('users.view') || $auth->hasPermission('content.view')): ?>
                    <li class="side-nav-title">Gestão</li>

                    <?php if ($auth->hasPermission('users.view')): ?>
                    <li class="side-nav-item">
                        <a href="users.php" class="side-nav-link">
                            <i class="ri-group-2-line"></i>
                            <span> Utilizadores </span>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php if ($auth->hasPermission('content.view')): ?>
                    <li class="side-nav-item">
                        <a href="content.php" class="side-nav-link">
                            <i class="ri-file-text-line"></i>
                            <span> Conteúdos </span>
                        </a>
                    </li>
                    <?php endif; ?>
                    <?php endif; ?>

                    <?php if ($auth->hasPermission('audit.view') || $auth->hasPermission('settings.view')): ?>
                    <li class="side-nav-title">Sistema</li>

                    <?php if ($auth->hasPermission('audit.view')): ?>
                    <li class="side-nav-item">
                        <a href="audit.php" class="side-nav-link">
                            <i class="ri-search-eye-line"></i>
                            <span> Auditoria </span>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php if ($auth->hasPermission('settings.view')): ?>
                    <li class="side-nav-item">
                        <a href="settings.php" class="side-nav-link">
                            <i class="ri-settings-4-line"></i>
                            <span> Configurações </span>
                        </a>
                    </li>
                    <?php endif; ?>
                    <?php endif; ?>

                </ul>
                <!--- End Sidemenu -->

                <div class="clearfix"></div>
            </div>
        </div>
        <!-- ========== Left Sidebar End ========== -->

        <!-- ============================================================== -->
        <!-- Start Page Content here -->
        <!-- ============================================================== -->

        <div class="content-page">
            <div class="content">

                <!-- Start Content-->
                <div class="container-fluid">

                    <!-- start page title -->
                    <div class="row">
                        <div class="col-12">
                            <div class="page-title-box">
                                <div class="page-title-right">
                                    <ol class="breadcrumb m-0">
                                        <li class="breadcrumb-item"><a href="javascript: void(0);">Santa Casa</a></li>
                                        <li class="breadcrumb-item"><a href="javascript: void(0);">Painel</a></li>
                                        <li class="breadcrumb-item"><a href="profile.php">Perfil</a></li>
                                        <li class="breadcrumb-item active">Alterar Password</li>
                                    </ol>
                                </div>
                                <h4 class="page-title">Alterar Password</h4>
                            </div>
                        </div>
                    </div>
                    <!-- end page title -->

                    <div class="row justify-content-center">
                        <div class="col-lg-8 col-xl-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="header-title">Alterar Password</h4>
                                    <p class="text-muted mb-0">
                                        Por segurança, introduza a sua password atual e defina uma nova password forte.
                                    </p>
                                </div>
                                <div class="card-body">

                                    <?php if ($error): ?>
                                    <div class="alert alert-danger" role="alert">
                                        <i class="ri-error-warning-line me-2"></i><?php echo htmlspecialchars($error); ?>
                                    </div>
                                    <?php endif; ?>

                                    <?php if ($success): ?>
                                    <div class="alert alert-success" role="alert">
                                        <i class="ri-check-line me-2"></i><?php echo htmlspecialchars($success); ?>
                                    </div>
                                    <?php endif; ?>

                                    <form method="POST" data-validate="true" id="changePasswordForm">
                                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                        <input type="hidden" name="change_password" value="1">

                                        <div class="mb-3">
                                            <label for="current_password" class="form-label">Password Atual</label>
                                            <div class="input-group input-group-merge">
                                                <input type="password" class="form-control" id="current_password" name="current_password"
                                                       placeholder="Introduza a sua password atual" required>
                                                <div class="input-group-text" data-password-toggle="current_password">
                                                    <span class="password-eye"></span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="new_password" class="form-label">Nova Password</label>
                                            <div class="input-group input-group-merge">
                                                <input type="password" class="form-control" id="new_password" name="new_password"
                                                       placeholder="Introduza a nova password" required>
                                                <div class="input-group-text" data-password-toggle="new_password">
                                                    <span class="password-eye"></span>
                                                </div>
                                            </div>
                                            <div class="password-strength-container mt-2">
                                                <div class="password-strength-bar">
                                                    <div class="password-strength-fill"></div>
                                                </div>
                                                <div class="password-strength-text"></div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="confirm_password" class="form-label">Confirmar Nova Password</label>
                                            <div class="input-group input-group-merge">
                                                <input type="password" class="form-control" id="confirm_password" name="confirm_password"
                                                       placeholder="Confirme a nova password" required>
                                                <div class="input-group-text" data-password-toggle="confirm_password">
                                                    <span class="password-eye"></span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="alert alert-info">
                                            <h6 class="alert-heading">Requisitos da Password:</h6>
                                            <ul class="mb-0">
                                                <li>Mínimo de 8 caracteres</li>
                                                <li>Pelo menos 1 letra minúscula</li>
                                                <li>Pelo menos 1 letra maiúscula</li>
                                                <li>Pelo menos 1 número</li>
                                                <li>Pelo menos 1 símbolo (@$!%*?&)</li>
                                            </ul>
                                        </div>

                                        <div class="text-end">
                                            <a href="profile.php" class="btn btn-light me-2">
                                                <i class="ri-arrow-left-line me-1"></i> Voltar ao Perfil
                                            </a>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="ri-save-line me-1"></i> Alterar Password
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- end row -->

                </div> <!-- container -->

            </div> <!-- content -->

            <!-- Footer Start -->
            <footer class="footer">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-md-6">
                            <script>document.write(new Date().getFullYear())</script> © Santa Casa da Misericórdia da Covilhã
                        </div>
                        <div class="col-md-6">
                            <div class="text-md-end footer-links d-none d-md-block">
                                <a href="javascript: void(0);">Sobre</a>
                                <a href="javascript: void(0);">Suporte</a>
                                <a href="javascript: void(0);">Contacto</a>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
            <!-- end Footer -->

        </div>

        <!-- ============================================================== -->
        <!-- End Page content -->
        <!-- ============================================================== -->

    </div>
    <!-- END wrapper -->

    <!-- Vendor js -->
    <script src="assets/js/vendor.min.js"></script>

    <!-- App js -->
    <script src="assets/js/app.min.js"></script>

    <!-- Custom Admin js -->
    <script src="assets/js/admin.js"></script>

    <script>
        // Password confirmation validation
        document.getElementById('changePasswordForm').addEventListener('submit', function(e) {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;

            if (newPassword !== confirmPassword) {
                e.preventDefault();
                alert('A nova password e a confirmação não coincidem.');
                return false;
            }
        });
    </script>

</body>
</html>
