<?php
/**
 * Página de Login
 * Santa Casa da Misericórdia da Covilhã
 */

// Inicializar configuração
require_once 'config/config.php';

// Inicializar middleware de segurança
$security = new SecurityMiddleware();
$security->handle();

// Inicializar autenticação
$auth = new Auth();

// Se já estiver autenticado, redirecionar
if ($auth->isLoggedIn()) {
    header('Location: index.php');
    exit();
}

$error = '';
$success = '';

// Processar login
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);
    
    if (empty($username) || empty($password)) {
        $error = 'Por favor, preencha todos os campos.';
    } else {
        // Verificar rate limiting específico para login
        $security->checkRateLimit('login', 5, 900); // 5 tentativas por 15 minutos
        
        $result = $auth->login($username, $password, $remember);
        
        if ($result['success']) {
            // Redirecionar para dashboard
            header('Location: index.php');
            exit();
        } else {
            $error = $result['message'];
        }
    }
}

$pageTitle = 'Iniciar Sessão';
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="utf-8" />
    <title><?php echo $pageTitle; ?> - <?php echo APP_NAME; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
    <meta content="Painel administrativo da Santa Casa da Misericórdia da Covilhã" name="description" />
    <meta content="Santa Casa da Misericórdia da Covilhã" name="author" />

    <!-- App favicon -->
    <link rel="shortcut icon" href="assets/images/favicon.ico">

    <!-- Theme Config Js -->
    <script src="assets/js/config.js"></script>

    <!-- App css -->
    <link href="assets/css/app.min.css" rel="stylesheet" type="text/css" id="app-style" />

    <!-- Icons css -->
    <link href="assets/css/icons.min.css" rel="stylesheet" type="text/css" />

    <!-- Custom Admin CSS -->
    <link href="assets/css/admin.css" rel="stylesheet" type="text/css">
</head>

<body class="authentication-bg position-relative">
    <div class="account-pages pt-2 pt-sm-5 pb-4 pb-sm-5 position-relative">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-xxl-8 col-lg-10">
                    <div class="card overflow-hidden">
                        <div class="row g-0">
                            <div class="col-lg-6 d-none d-lg-block p-2">
                                <img src="assets/images/auth-img.jpg" alt="" class="img-fluid rounded h-100">
                            </div>
                            <div class="col-lg-6">
                                <div class="d-flex flex-column h-100">
                                    <!-- Removed duplicate logo from auth-brand section -->
                                    <div class="p-4 my-auto">
                                        <!-- Official Logo above login form -->
                                        <div class="login-logo-container">
                                            <img src="assets/images/logos/logotipo-preto.svg" alt="Santa Casa da Misericórdia da Covilhã" class="img-fluid login-logo-large">
                                        </div>

                                        <h4 class="fs-20">Iniciar Sessão</h4>

                                        <?php if ($error): ?>
                                        <div class="alert alert-danger" role="alert">
                                            <i class="ri-error-warning-line me-2"></i><?php echo htmlspecialchars($error); ?>
                                        </div>
                                        <?php endif; ?>

                                        <?php if ($success): ?>
                                        <div class="alert alert-success" role="alert">
                                            <i class="ri-check-line me-2"></i><?php echo htmlspecialchars($success); ?>
                                        </div>
                                        <?php endif; ?>

                                        <!-- form -->
                                        <form method="POST" data-validate="true" class="authentication-form" novalidate>
                                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                                            <div class="mb-3">
                                                <label for="username" class="form-label">Nome de Utilizador ou Email</label>
                                                <input class="form-control" type="text" id="username" name="username" required
                                                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                                                       placeholder="Digite o seu nome de utilizador ou email"
                                                       autocomplete="username" autofocus>
                                            </div>

                                            <div class="mb-3">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <label for="password" class="form-label mb-0">Password</label>
                                                    <a href="forgot-password.php" class="text-muted">
                                                        <small>Esqueceu a sua password?</small>
                                                    </a>
                                                </div>
                                                <input class="form-control" type="password" required id="password" name="password"
                                                       placeholder="Digite a sua password" autocomplete="current-password">
                                            </div>

                                            <div class="mb-4">
                                                <div class="form-check">
                                                    <input type="checkbox" class="form-check-input" id="remember" name="remember" value="1">
                                                    <label class="form-check-label" for="remember">Manter sessão iniciada</label>
                                                </div>
                                            </div>

                                            <div class="mb-0 text-start">
                                                <button class="btn btn-primary w-100" type="submit">
                                                    <i class="ri-login-circle-fill me-2"></i>
                                                    <span class="fw-bold">Iniciar Sessão</span>
                                                </button>
                                            </div>
                                        </form>
                                        <!-- end form-->
                                    </div>
                                </div>
                            </div> <!-- end col -->
                        </div>
                    </div>
                </div>
                <!-- end row -->
            </div>
            <!-- end container -->
        </div>
        <!-- end page -->

        <footer class="footer footer-alt fw-medium">
            <span class="text-light">
                <script>document.write(new Date().getFullYear())</script> © Santa Casa da Misericórdia da Covilhã
            </span>
        </footer>
    </div>
    <!-- end authentication-bg -->

    <!-- Vendor js -->
    <script src="assets/js/vendor.min.js"></script>

    <!-- App js -->
    <script src="assets/js/app.min.js"></script>

    <!-- Custom Admin js -->
    <script src="assets/js/admin.js"></script>

</body>
</html>
