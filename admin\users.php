<?php
/**
 * Gestão de Utilizadores
 * Santa Casa da Misericórdia da Covilhã
 */

// Inicializar configuração
require_once 'config/config.php';

// Inicializar middleware de segurança
$security = new SecurityMiddleware();
$security->handle();

// Inicializar autenticação
$auth = new Auth();

// Verificar se o utilizador está autenticado
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit();
}

// Obter utilizador atual
$currentUser = $auth->getCurrentUser();

// Verificar permissões
if (!$auth->hasPermission('users.view')) {
    header('Location: unauthorized.php');
    exit();
}

// Obter lista de utilizadores
$db = new Database();
$users = $db->fetchAll("
    SELECT u.*, r.display_name as role_name 
    FROM admin_users u 
    LEFT JOIN admin_roles r ON u.role_id = r.id 
    ORDER BY u.created_at DESC
");

$pageTitle = 'Gestão de Utilizadores';
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="utf-8" />
    <title><?php echo $pageTitle; ?> - <?php echo APP_NAME; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
    <meta content="Gestão de utilizadores da Santa Casa da Misericórdia da Covilhã" name="description" />
    <meta content="Santa Casa da Misericórdia da Covilhã" name="author" />

    <!-- App favicon -->
    <link rel="shortcut icon" href="assets/images/favicon.ico">

    <!-- Datatables css -->
    <link href="assets/vendor/datatables.net-bs5/css/dataTables.bootstrap5.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/vendor/datatables.net-responsive-bs5/css/responsive.bootstrap5.min.css" rel="stylesheet" type="text/css" />

    <!-- Theme Config Js -->
    <script src="assets/js/config.js"></script>

    <!-- App css -->
    <link href="assets/css/app.min.css" rel="stylesheet" type="text/css" id="app-style" />

    <!-- Icons css -->
    <link href="assets/css/icons.min.css" rel="stylesheet" type="text/css" />

    <!-- Custom Admin css -->
    <link href="assets/css/admin.css" rel="stylesheet" type="text/css" />
</head>

<body>
    <!-- Begin page -->
    <div class="wrapper">

        <!-- ========== Topbar Start ========== -->
        <div class="navbar-custom">
            <div class="topbar container-fluid">
                <div class="d-flex align-items-center gap-1">

                    <!-- Topbar Brand Logo -->
                    <div class="logo-topbar">
                        <!-- Logo light -->
                        <a href="index.php" class="logo-light">
                            <span class="logo-lg">
                                <img src="assets/images/logo-custom.svg" alt="Santa Casa da Misericórdia da Covilhã" height="30">
                            </span>
                            <span class="logo-sm">
                                <img src="assets/images/logo-sm-custom.svg" alt="Santa Casa" height="30">
                            </span>
                        </a>

                        <!-- Logo Dark -->
                        <a href="index.php" class="logo-dark">
                            <span class="logo-lg">
                                <img src="assets/images/logo-custom.svg" alt="Santa Casa da Misericórdia da Covilhã" height="30">
                            </span>
                            <span class="logo-sm">
                                <img src="assets/images/logo-sm-custom.svg" alt="Santa Casa" height="30">
                            </span>
                        </a>
                    </div>

                    <!-- Sidebar Menu Toggle Button -->
                    <button class="button-toggle-menu">
                        <i class="ri-menu-line"></i>
                    </button>

                    <!-- Horizontal Menu Toggle Button -->
                    <button class="navbar-toggle" data-bs-toggle="collapse" data-bs-target="#topnav-menu-content">
                        <div class="lines">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </button>

                    <!-- Topbar Search Form -->
                    <div class="app-search d-none d-lg-block">
                        <form>
                            <div class="input-group">
                                <input type="search" class="form-control" placeholder="Pesquisar...">
                                <span class="ri-search-line search-icon text-muted"></span>
                            </div>
                        </form>
                    </div>
                </div>

                <ul class="topbar-menu d-flex align-items-center gap-3">
                    <li class="dropdown d-lg-none">
                        <a class="nav-link dropdown-toggle arrow-none" data-bs-toggle="dropdown" href="#" role="button"
                            aria-haspopup="false" aria-expanded="false">
                            <i class="ri-search-line fs-22"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-animated dropdown-lg p-0">
                            <form class="p-3">
                                <input type="search" class="form-control" placeholder="Pesquisar..."
                                    aria-label="Pesquisar">
                            </form>
                        </div>
                    </li>

                    <li class="dropdown notification-list">
                        <a class="nav-link dropdown-toggle arrow-none" data-bs-toggle="dropdown" href="#" role="button"
                            aria-haspopup="false" aria-expanded="false">
                            <i class="ri-notification-3-line fs-22"></i>
                            <span class="noti-icon-badge badge text-bg-pink">0</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end dropdown-menu-animated dropdown-lg py-0">
                            <div class="p-2 border-top-0 border-start-0 border-end-0 border-dashed border">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h6 class="m-0 fs-16 fw-semibold">Notificações</h6>
                                    </div>
                                    <div class="col-auto">
                                        <a href="javascript: void(0);" class="text-dark text-decoration-underline">
                                            <small>Limpar Todas</small>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="px-1" style="max-height: 300px;" data-simplebar>
                                <h5 class="text-muted fs-13 fw-normal mt-2">Hoje</h5>
                                <p class="text-muted text-center py-3">Nenhuma notificação</p>
                            </div>
                            <a href="javascript:void(0);" class="dropdown-item text-center text-primary notify-item border-top py-2">
                                Ver Todas
                            </a>
                        </div>
                    </li>

                    <li class="dropdown">
                        <a class="nav-link dropdown-toggle arrow-none nav-user" data-bs-toggle="dropdown" href="#" role="button"
                            aria-haspopup="false" aria-expanded="false">
                            <span class="account-user-avatar">
                                <div style="width: 32px; height: 32px; border-radius: 50%; background: linear-gradient(135deg, #2c5aa0, #1e3d6f); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 0.8rem;">
                                    <?php echo strtoupper(substr($currentUser['first_name'], 0, 1) . substr($currentUser['last_name'], 0, 1)); ?>
                                </div>
                            </span>
                            <span class="d-lg-block d-none">
                                <h5 class="my-0 fw-normal"><?php echo htmlspecialchars($currentUser['first_name'] . ' ' . $currentUser['last_name']); ?> <i class="ri-arrow-down-s-line d-none d-sm-inline-block align-middle"></i></h5>
                            </span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end dropdown-menu-animated profile-dropdown">
                            <!-- item-->
                            <div class=" dropdown-header noti-title">
                                <h6 class="text-overflow m-0">Bem-vindo!</h6>
                            </div>

                            <!-- item-->
                            <a href="profile.php" class="dropdown-item notify-item">
                                <i class="ri-account-circle-line fs-16 align-middle"></i>
                                <span>Meu Perfil</span>
                            </a>

                            <!-- item-->
                            <a href="change-password.php" class="dropdown-item notify-item">
                                <i class="ri-settings-4-line fs-16 align-middle"></i>
                                <span>Alterar Password</span>
                            </a>

                            <!-- item-->
                            <a href="logout.php" class="dropdown-item notify-item">
                                <i class="ri-logout-box-line fs-16 align-middle"></i>
                                <span>Terminar Sessão</span>
                            </a>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        <!-- ========== Topbar End ========== -->

        <!-- ========== Left Sidebar Start ========== -->
        <div class="leftside-menu">

            <!-- Brand Logo Light -->
            <a href="index.php" class="logo logo-light">
                <span class="logo-lg">
                    <img src="assets/images/logo-custom.svg" alt="Santa Casa da Misericórdia da Covilhã" height="35">
                </span>
                <span class="logo-sm">
                    <img src="assets/images/logo-sm-custom.svg" alt="Santa Casa" height="35">
                </span>
            </a>

            <!-- Brand Logo Dark -->
            <a href="index.php" class="logo logo-dark">
                <span class="logo-lg">
                    <img src="assets/images/logo-custom.svg" alt="Santa Casa da Misericórdia da Covilhã" height="35">
                </span>
                <span class="logo-sm">
                    <img src="assets/images/logo-sm-custom.svg" alt="Santa Casa" height="35">
                </span>
            </a>

            <!-- Sidebar -left -->
            <div class="h-100" id="leftside-menu-container" data-simplebar>
                <!--- Sidemenu -->
                <ul class="side-nav">

                    <li class="side-nav-title">Principal</li>

                    <li class="side-nav-item">
                        <a href="index.php" class="side-nav-link">
                            <i class="ri-dashboard-3-line"></i>
                            <span> Painel de Controlo </span>
                        </a>
                    </li>

                    <?php if ($auth->hasPermission('users.view') || $auth->hasPermission('content.view')): ?>
                    <li class="side-nav-title">Gestão</li>

                    <?php if ($auth->hasPermission('users.view')): ?>
                    <li class="side-nav-item">
                        <a href="users.php" class="side-nav-link active">
                            <i class="ri-group-2-line"></i>
                            <span> Utilizadores </span>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php if ($auth->hasPermission('content.view')): ?>
                    <li class="side-nav-item">
                        <a href="content.php" class="side-nav-link">
                            <i class="ri-file-text-line"></i>
                            <span> Conteúdos </span>
                        </a>
                    </li>
                    <?php endif; ?>
                    <?php endif; ?>

                    <?php if ($auth->hasPermission('audit.view') || $auth->hasPermission('settings.view')): ?>
                    <li class="side-nav-title">Sistema</li>

                    <?php if ($auth->hasPermission('audit.view')): ?>
                    <li class="side-nav-item">
                        <a href="audit.php" class="side-nav-link">
                            <i class="ri-search-eye-line"></i>
                            <span> Auditoria </span>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php if ($auth->hasPermission('settings.view')): ?>
                    <li class="side-nav-item">
                        <a href="settings.php" class="side-nav-link">
                            <i class="ri-settings-4-line"></i>
                            <span> Configurações </span>
                        </a>
                    </li>
                    <?php endif; ?>
                    <?php endif; ?>

                </ul>
                <!--- End Sidemenu -->

                <div class="clearfix"></div>
            </div>
        </div>
        <!-- ========== Left Sidebar End ========== -->

        <!-- ============================================================== -->
        <!-- Start Page Content here -->
        <!-- ============================================================== -->

        <div class="content-page">
            <div class="content">

                <!-- Start Content-->
                <div class="container-fluid">

                    <!-- start page title -->
                    <div class="row">
                        <div class="col-12">
                            <div class="page-title-box">
                                <div class="page-title-right">
                                    <ol class="breadcrumb m-0">
                                        <li class="breadcrumb-item"><a href="javascript: void(0);">Santa Casa</a></li>
                                        <li class="breadcrumb-item"><a href="javascript: void(0);">Gestão</a></li>
                                        <li class="breadcrumb-item active">Utilizadores</li>
                                    </ol>
                                </div>
                                <h4 class="page-title">Gestão de Utilizadores</h4>
                            </div>
                        </div>
                    </div>
                    <!-- end page title -->

                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <div class="row mb-2">
                                        <div class="col-xl-8">
                                            <form class="row gy-2 gx-2 align-items-center justify-content-xl-start justify-content-between">
                                                <div class="col-auto">
                                                    <label for="inputPassword2" class="visually-hidden">Pesquisar</label>
                                                    <input type="search" class="form-control" id="inputPassword2" placeholder="Pesquisar utilizadores...">
                                                </div>
                                                <div class="col-auto">
                                                    <button type="submit" class="btn btn-primary">Pesquisar</button>
                                                </div>
                                            </form>
                                        </div>
                                        <div class="col-xl-4">
                                            <div class="text-xl-end mt-xl-0 mt-2">
                                                <?php if ($auth->hasPermission('users.create')): ?>
                                                <a href="user-create.php" class="btn btn-success mb-2 me-2">
                                                    <i class="ri-add-circle-line me-1"></i> Adicionar Utilizador
                                                </a>
                                                <?php endif; ?>
                                                <a href="javascript:void(0);" class="btn btn-light mb-2">Exportar</a>
                                            </div>
                                        </div><!-- end col-->
                                    </div>

                                    <div class="table-responsive">
                                        <table class="table table-centered table-nowrap mb-0" id="users-table">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>Utilizador</th>
                                                    <th>Email</th>
                                                    <th>Função</th>
                                                    <th>Estado</th>
                                                    <th>Último Login</th>
                                                    <th style="width: 125px;">Ações</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($users as $user): ?>
                                                <tr>
                                                    <td class="table-user">
                                                        <div class="d-flex align-items-center">
                                                            <div class="flex-shrink-0">
                                                                <div class="avatar-sm rounded-circle bg-primary d-flex align-items-center justify-content-center text-white fw-bold">
                                                                    <?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?>
                                                                </div>
                                                            </div>
                                                            <div class="flex-grow-1 ms-2">
                                                                <h5 class="my-0 fw-normal"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></h5>
                                                                <p class="text-muted mb-0">@<?php echo htmlspecialchars($user['username']); ?></p>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                                                    <td><span class="badge bg-info-subtle text-info"><?php echo htmlspecialchars($user['role_name']); ?></span></td>
                                                    <td>
                                                        <?php if ($user['is_active']): ?>
                                                        <span class="badge bg-success-subtle text-success">Ativo</span>
                                                        <?php else: ?>
                                                        <span class="badge bg-danger-subtle text-danger">Inativo</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td><?php echo $user['last_login'] ? date('d/m/Y H:i', strtotime($user['last_login'])) : 'Nunca'; ?></td>
                                                    <td>
                                                        <div class="d-flex gap-2">
                                                            <?php if ($auth->hasPermission('users.edit')): ?>
                                                            <a href="user-edit.php?id=<?php echo $user['id']; ?>" class="btn btn-light btn-sm">
                                                                <i class="ri-edit-box-line"></i>
                                                            </a>
                                                            <?php endif; ?>
                                                            <?php if ($auth->hasPermission('users.delete') && $user['id'] != $currentUser['id']): ?>
                                                            <a href="javascript:void(0);" class="btn btn-danger btn-sm" onclick="deleteUser(<?php echo $user['id']; ?>)">
                                                                <i class="ri-delete-bin-line"></i>
                                                            </a>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div> <!-- end card-body-->
                            </div> <!-- end card-->
                        </div> <!-- end col -->
                    </div>
                    <!-- end row -->

                </div> <!-- container -->

            </div> <!-- content -->

            <!-- Footer Start -->
            <footer class="footer">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-md-6">
                            <script>document.write(new Date().getFullYear())</script> © Santa Casa da Misericórdia da Covilhã
                        </div>
                        <div class="col-md-6">
                            <div class="text-md-end footer-links d-none d-md-block">
                                <a href="javascript: void(0);">Sobre</a>
                                <a href="javascript: void(0);">Suporte</a>
                                <a href="javascript: void(0);">Contacto</a>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
            <!-- end Footer -->

        </div>

        <!-- ============================================================== -->
        <!-- End Page content -->
        <!-- ============================================================== -->

    </div>
    <!-- END wrapper -->

    <!-- Vendor js -->
    <script src="assets/js/vendor.min.js"></script>

    <!-- Datatables js -->
    <script src="assets/vendor/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="assets/vendor/datatables.net-bs5/js/dataTables.bootstrap5.min.js"></script>
    <script src="assets/vendor/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
    <script src="assets/vendor/datatables.net-responsive-bs5/js/responsive.bootstrap5.min.js"></script>

    <!-- App js -->
    <script src="assets/js/app.min.js"></script>

    <!-- Custom Admin js -->
    <script src="assets/js/admin.js"></script>

    <script>
        $(document).ready(function() {
            $('#users-table').DataTable({
                language: {
                    url: 'assets/vendor/datatables.net/i18n/Portuguese.json'
                },
                responsive: true,
                pageLength: 25
            });
        });

        function deleteUser(userId) {
            AdminPanel.showConfirmation(
                'Tem a certeza que deseja eliminar este utilizador?',
                function() {
                    // Implement delete functionality
                    console.log('Delete user:', userId);
                    AdminPanel.showNotification('Utilizador eliminado com sucesso!', 'success');
                }
            );
        }
    </script>

</body>
</html>
