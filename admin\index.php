<?php
/**
 * Página Principal do Painel Administrativo
 * Santa Casa da Misericórdia da Covilhã
 */

// Inicializar configuração
require_once 'config/config.php';

// Inicializar middleware de segurança
$security = new SecurityMiddleware();
$security->handle();

// Inicializar autenticação
$auth = new Auth();

// Verificar se é a primeira instalação
$db = new Database();
$hasUsers = $db->fetch("SELECT COUNT(*) as count FROM admin_users");

if ($hasUsers['count'] == 0) {
    // Redirecionar para setup inicial
    header('Location: setup.php');
    exit();
}

// Verificar se o utilizador está autenticado
if (!$auth->isLoggedIn()) {
    // Redirecionar para login
    header('Location: login.php');
    exit();
}

// Obter utilizador atual
$currentUser = $auth->getCurrentUser();

// Verificar permissões para dashboard
if (!$auth->hasPermission('dashboard.view')) {
    header('Location: unauthorized.php');
    exit();
}

// Obter estatísticas do dashboard
$stats = [
    'total_users' => $db->fetch("SELECT COUNT(*) as count FROM admin_users WHERE is_active = 1")['count'],
    'total_sessions' => $db->fetch("SELECT COUNT(*) as count FROM admin_sessions WHERE last_activity > ?", [time() - 3600])['count'],
    'recent_logins' => $db->fetchAll("SELECT COUNT(*) as count, DATE(last_login) as date FROM admin_users WHERE last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY) GROUP BY DATE(last_login) ORDER BY date DESC"),
    'security_events' => $db->fetch("SELECT COUNT(*) as count FROM admin_audit_log WHERE action = 'security_event' AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)")['count']
];

$pageTitle = 'Painel de Controlo';
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="utf-8" />
    <title><?php echo $pageTitle; ?> - <?php echo APP_NAME; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
    <meta content="Painel administrativo da Santa Casa da Misericórdia da Covilhã" name="description" />
    <meta content="Santa Casa da Misericórdia da Covilhã" name="author" />

    <!-- App favicon -->
    <link rel="shortcut icon" href="assets/images/favicon.ico">

    <!-- Daterangepicker css -->
    <link rel="stylesheet" href="assets/vendor/daterangepicker/daterangepicker.css">

    <!-- Vector Map css -->
    <link rel="stylesheet" href="assets/vendor/admin-resources/jquery.vectormap/jquery-jvectormap-1.2.2.css">

    <!-- Theme Config Js -->
    <script src="assets/js/config.js"></script>

    <!-- App css -->
    <link href="assets/css/app.min.css" rel="stylesheet" type="text/css" id="app-style" />

    <!-- Icons css -->
    <link href="assets/css/icons.min.css" rel="stylesheet" type="text/css" />

    <!-- Custom Admin css -->
    <link href="assets/css/admin.css" rel="stylesheet" type="text/css" />
</head>

<body>
    <!-- Begin page -->
    <div class="wrapper">

        <!-- ========== Topbar Start ========== -->
        <div class="navbar-custom">
            <div class="topbar container-fluid">
                <div class="d-flex align-items-center gap-1">

                    <!-- Topbar Brand Logo -->
                    <div class="logo-topbar">
                        <!-- Logo light -->
                        <a href="index.php" class="logo-light">
                            <span class="logo-lg">
                                <img src="assets/images/logos/logotipo-branco.svg" alt="Santa Casa da Misericórdia da Covilhã" height="30">
                            </span>
                            <span class="logo-sm">
                                <img src="assets/images/logos/brasao-branco.svg" alt="Santa Casa" height="30">
                            </span>
                        </a>

                        <!-- Logo Dark -->
                        <a href="index.php" class="logo-dark">
                            <span class="logo-lg">
                                <img src="assets/images/logos/logotipo-preto.svg" alt="Santa Casa da Misericórdia da Covilhã" height="30">
                            </span>
                            <span class="logo-sm">
                                <img src="assets/images/logos/brasao-preto.svg" alt="Santa Casa" height="30">
                            </span>
                        </a>
                    </div>

                    <!-- Sidebar Menu Toggle Button -->
                    <button class="button-toggle-menu">
                        <i class="ri-menu-line"></i>
                    </button>

                    <!-- Horizontal Menu Toggle Button -->
                    <button class="navbar-toggle" data-bs-toggle="collapse" data-bs-target="#topnav-menu-content">
                        <div class="lines">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </button>

                    <!-- Topbar Search Form -->
                    <div class="app-search d-none d-lg-block">
                        <form>
                            <div class="input-group">
                                <input type="search" class="form-control" placeholder="Pesquisar...">
                                <span class="ri-search-line search-icon text-muted"></span>
                            </div>
                        </form>
                    </div>
                </div>

                <ul class="topbar-menu d-flex align-items-center gap-3">
                    <li class="dropdown d-lg-none">
                        <a class="nav-link dropdown-toggle arrow-none" data-bs-toggle="dropdown" href="#" role="button"
                            aria-haspopup="false" aria-expanded="false">
                            <i class="ri-search-line fs-22"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-animated dropdown-lg p-0">
                            <form class="p-3">
                                <input type="search" class="form-control" placeholder="Pesquisar..."
                                    aria-label="Pesquisar">
                            </form>
                        </div>
                    </li>

                    <li class="dropdown notification-list">
                        <a class="nav-link dropdown-toggle arrow-none" data-bs-toggle="dropdown" href="#" role="button"
                            aria-haspopup="false" aria-expanded="false">
                            <i class="ri-notification-3-line fs-22"></i>
                            <span class="noti-icon-badge badge text-bg-pink"><?php echo $stats['security_events']; ?></span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end dropdown-menu-animated dropdown-lg py-0">
                            <div class="p-2 border-top-0 border-start-0 border-end-0 border-dashed border">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h6 class="m-0 fs-16 fw-semibold">Notificações</h6>
                                    </div>
                                    <div class="col-auto">
                                        <a href="javascript: void(0);" class="text-dark text-decoration-underline">
                                            <small>Limpar Todas</small>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="px-1" style="max-height: 300px;" data-simplebar>
                                <h5 class="text-muted fs-13 fw-normal mt-2">Hoje</h5>
                                <!-- notification item-->
                                <a href="javascript:void(0);" class="dropdown-item p-0 notify-item card unread-noti shadow-none mb-1">
                                    <div class="card-body">
                                        <span class="float-end noti-close-btn text-muted"><i class="ri-close-line fs-18"></i></span>
                                        <div class="d-flex align-items-center">
                                            <div class="flex-shrink-0">
                                                <div class="notify-icon bg-primary">
                                                    <i class="ri-message-3-line fs-18"></i>
                                                </div>
                                            </div>
                                            <div class="flex-grow-1 text-truncate ms-2">
                                                <h5 class="noti-item-title fw-semibold fs-14">Sistema <small class="fw-normal text-muted ms-1">há 1 min</small></h5>
                                                <small class="noti-item-subtitle text-muted">Backup automático concluído com sucesso</small>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </div>
                            <a href="javascript:void(0);" class="dropdown-item text-center text-primary notify-item border-top py-2">
                                Ver Todas
                            </a>
                        </div>
                    </li>

                    <li class="dropdown">
                        <a class="nav-link dropdown-toggle arrow-none nav-user" data-bs-toggle="dropdown" href="#" role="button"
                            aria-haspopup="false" aria-expanded="false">
                            <span class="account-user-avatar">
                                <div style="width: 32px; height: 32px; border-radius: 50%; background: linear-gradient(135deg, #2c5aa0, #1e3d6f); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 0.8rem;">
                                    <?php echo strtoupper(substr($currentUser['first_name'], 0, 1) . substr($currentUser['last_name'], 0, 1)); ?>
                                </div>
                            </span>
                            <span class="d-lg-block d-none">
                                <h5 class="my-0 fw-normal"><?php echo htmlspecialchars($currentUser['first_name'] . ' ' . $currentUser['last_name']); ?> <i class="ri-arrow-down-s-line d-none d-sm-inline-block align-middle"></i></h5>
                            </span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end dropdown-menu-animated profile-dropdown">
                            <!-- item-->
                            <div class=" dropdown-header noti-title">
                                <h6 class="text-overflow m-0">Bem-vindo!</h6>
                            </div>

                            <!-- item-->
                            <a href="profile.php" class="dropdown-item notify-item">
                                <i class="ri-account-circle-line fs-16 align-middle"></i>
                                <span>Meu Perfil</span>
                            </a>

                            <!-- item-->
                            <a href="change-password.php" class="dropdown-item notify-item">
                                <i class="ri-settings-4-line fs-16 align-middle"></i>
                                <span>Alterar Password</span>
                            </a>

                            <!-- item-->
                            <a href="logout.php" class="dropdown-item notify-item">
                                <i class="ri-logout-box-line fs-16 align-middle"></i>
                                <span>Terminar Sessão</span>
                            </a>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        <!-- ========== Topbar End ========== -->

        <!-- ========== Left Sidebar Start ========== -->
        <div class="leftside-menu">

            <!-- Brand Logo Light -->
            <a href="index.php" class="logo logo-light">
                <span class="logo-lg">
                    <img src="assets/images/logos/logotipo-branco.svg" alt="Santa Casa da Misericórdia da Covilhã" height="45">
                </span>
                <span class="logo-sm">
                    <img src="assets/images/logos/brasao-branco.svg" alt="Santa Casa" height="45">
                </span>
            </a>

            <!-- Brand Logo Dark -->
            <a href="index.php" class="logo logo-dark">
                <span class="logo-lg">
                    <img src="assets/images/logos/logotipo-preto.svg" alt="Santa Casa da Misericórdia da Covilhã" height="45">
                </span>
                <span class="logo-sm">
                    <img src="assets/images/logos/brasao-preto.svg" alt="Santa Casa" height="45">
                </span>
            </a>

            <!-- Sidebar -left -->
            <div class="h-100" id="leftside-menu-container" data-simplebar>
                <!--- Sidemenu -->
                <ul class="side-nav">

                    <li class="side-nav-title">Principal</li>

                    <li class="side-nav-item">
                        <a href="index.php" class="side-nav-link active">
                            <i class="ri-dashboard-3-line"></i>
                            <span> Painel de Controlo </span>
                        </a>
                    </li>

                    <?php if ($auth->hasPermission('users.view') || $auth->hasPermission('content.view')): ?>
                    <li class="side-nav-title">Gestão</li>

                    <?php if ($auth->hasPermission('users.view')): ?>
                    <li class="side-nav-item">
                        <a href="users.php" class="side-nav-link">
                            <i class="ri-group-2-line"></i>
                            <span> Utilizadores </span>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php if ($auth->hasPermission('content.view')): ?>
                    <li class="side-nav-item">
                        <a href="content.php" class="side-nav-link">
                            <i class="ri-file-text-line"></i>
                            <span> Conteúdos </span>
                        </a>
                    </li>
                    <?php endif; ?>
                    <?php endif; ?>

                    <?php if ($auth->hasPermission('audit.view') || $auth->hasPermission('settings.view')): ?>
                    <li class="side-nav-title">Sistema</li>

                    <?php if ($auth->hasPermission('audit.view')): ?>
                    <li class="side-nav-item">
                        <a href="audit.php" class="side-nav-link">
                            <i class="ri-search-eye-line"></i>
                            <span> Auditoria </span>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php if ($auth->hasPermission('settings.view')): ?>
                    <li class="side-nav-item">
                        <a href="settings.php" class="side-nav-link">
                            <i class="ri-settings-4-line"></i>
                            <span> Configurações </span>
                        </a>
                    </li>
                    <?php endif; ?>
                    <?php endif; ?>

                </ul>
                <!--- End Sidemenu -->

                <div class="clearfix"></div>
            </div>
        </div>
        <!-- ========== Left Sidebar End ========== -->

        <!-- ============================================================== -->
        <!-- Start Page Content here -->
        <!-- ============================================================== -->

        <div class="content-page">
            <div class="content">

                <!-- Start Content-->
                <div class="container-fluid">

                    <!-- start page title -->
                    <div class="row">
                        <div class="col-12">
                            <div class="page-title-box">
                                <div class="page-title-right">
                                    <ol class="breadcrumb m-0">
                                        <li class="breadcrumb-item"><a href="javascript: void(0);">Santa Casa</a></li>
                                        <li class="breadcrumb-item"><a href="javascript: void(0);">Painel</a></li>
                                        <li class="breadcrumb-item active">Visão Geral</li>
                                    </ol>
                                </div>
                                <h4 class="page-title">Bem-vindo, <?php echo htmlspecialchars($currentUser['first_name']); ?>!</h4>
                            </div>
                        </div>
                    </div>
                    <!-- end page title -->

                    <!-- Statistics Cards -->
                    <div class="row">
                        <div class="col-xxl-3 col-sm-6">
                            <div class="card widget-flat text-bg-primary">
                                <div class="card-body">
                                    <div class="float-end">
                                        <i class="ri-group-2-line widget-icon"></i>
                                    </div>
                                    <h6 class="text-uppercase mt-0" title="Utilizadores">Utilizadores Ativos</h6>
                                    <h2 class="my-2"><?php echo $stats['total_users']; ?></h2>
                                    <p class="mb-0">
                                        <span class="badge bg-white bg-opacity-10 me-1">Ativo</span>
                                        <span class="text-nowrap">Total de utilizadores</span>
                                    </p>
                                </div>
                            </div>
                        </div> <!-- end col-->

                        <div class="col-xxl-3 col-sm-6">
                            <div class="card widget-flat text-bg-success">
                                <div class="card-body">
                                    <div class="float-end">
                                        <i class="ri-user-line widget-icon"></i>
                                    </div>
                                    <h6 class="text-uppercase mt-0" title="Sessões">Sessões Ativas</h6>
                                    <h2 class="my-2"><?php echo $stats['total_sessions']; ?></h2>
                                    <p class="mb-0">
                                        <span class="badge bg-white bg-opacity-10 me-1">Online</span>
                                        <span class="text-nowrap">Última hora</span>
                                    </p>
                                </div>
                            </div>
                        </div> <!-- end col-->

                        <div class="col-xxl-3 col-sm-6">
                            <div class="card widget-flat text-bg-info">
                                <div class="card-body">
                                    <div class="float-end">
                                        <i class="ri-login-circle-line widget-icon"></i>
                                    </div>
                                    <h6 class="text-uppercase mt-0" title="Logins">Logins Recentes</h6>
                                    <h2 class="my-2"><?php echo count($stats['recent_logins']); ?></h2>
                                    <p class="mb-0">
                                        <span class="badge bg-white bg-opacity-25 me-1">7 dias</span>
                                        <span class="text-nowrap">Últimos logins</span>
                                    </p>
                                </div>
                            </div>
                        </div> <!-- end col-->

                        <div class="col-xxl-3 col-sm-6">
                            <div class="card widget-flat text-bg-warning">
                                <div class="card-body">
                                    <div class="float-end">
                                        <i class="ri-shield-check-line widget-icon"></i>
                                    </div>
                                    <h6 class="text-uppercase mt-0" title="Segurança">Eventos de Segurança</h6>
                                    <h2 class="my-2"><?php echo $stats['security_events']; ?></h2>
                                    <p class="mb-0">
                                        <span class="badge bg-white bg-opacity-10 me-1">24h</span>
                                        <span class="text-nowrap">Últimas 24 horas</span>
                                    </p>
                                </div>
                            </div>
                        </div> <!-- end col-->
                    </div>

                    <!-- Recent Activity and System Info -->
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-body">
                                    <div class="card-widgets">
                                        <a href="javascript:;" data-bs-toggle="reload"><i class="ri-refresh-line"></i></a>
                                        <a data-bs-toggle="collapse" href="#activity-collapse" role="button" aria-expanded="false" aria-controls="activity-collapse"><i class="ri-subtract-line"></i></a>
                                    </div>
                                    <h5 class="header-title mb-0">Atividade Recente</h5>

                                    <div id="activity-collapse" class="collapse pt-3 show">
                                        <?php
                                        $auditLog = new AuditLog();
                                        $recentActivity = $auditLog->getLogs([], 10);
                                        ?>

                                        <?php if (empty($recentActivity)): ?>
                                        <div class="text-center py-4">
                                            <i class="ri-information-line fs-48 text-muted"></i>
                                            <p class="text-muted mt-2">Nenhuma atividade recente.</p>
                                        </div>
                                        <?php else: ?>
                                        <div class="table-responsive">
                                            <table class="table table-sm table-centered mb-0">
                                                <thead>
                                                    <tr>
                                                        <th>Utilizador</th>
                                                        <th>Ação</th>
                                                        <th>Data/Hora</th>
                                                        <th>Estado</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($recentActivity as $activity): ?>
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <div class="flex-shrink-0">
                                                                    <div class="avatar-sm rounded-circle bg-light d-flex align-items-center justify-content-center">
                                                                        <i class="ri-user-line text-muted"></i>
                                                                    </div>
                                                                </div>
                                                                <div class="flex-grow-1 ms-2">
                                                                    <h5 class="my-0 fw-normal"><?php echo htmlspecialchars($activity['user_name'] ?: 'Sistema'); ?></h5>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td><?php echo htmlspecialchars($activity['action']); ?></td>
                                                        <td><?php echo date('d/m/Y H:i', strtotime($activity['created_at'])); ?></td>
                                                        <td><span class="badge bg-success-subtle text-success">Concluído</span></td>
                                                    </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div> <!-- end col -->

                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-body">
                                    <div class="card-widgets">
                                        <a href="javascript:;" data-bs-toggle="reload"><i class="ri-refresh-line"></i></a>
                                        <a data-bs-toggle="collapse" href="#system-collapse" role="button" aria-expanded="false" aria-controls="system-collapse"><i class="ri-subtract-line"></i></a>
                                    </div>
                                    <h5 class="header-title mb-0">Informações do Sistema</h5>

                                    <div id="system-collapse" class="collapse pt-3 show">
                                        <div class="text-center">
                                            <div class="row">
                                                <div class="col-6">
                                                    <div class="p-2">
                                                        <h4 class="fw-normal">
                                                            <span><?php echo APP_VERSION; ?></span>
                                                        </h4>
                                                        <p class="text-muted mb-0">Versão</p>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="p-2">
                                                        <h4 class="fw-normal">
                                                            <span><?php echo PHP_VERSION; ?></span>
                                                        </h4>
                                                        <p class="text-muted mb-0">PHP</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mt-3">
                                            <div class="list-group list-group-flush">
                                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <h6 class="my-0">Último Login</h6>
                                                        <small class="text-muted"><?php echo $currentUser['last_login'] ? date('d/m/Y H:i', strtotime($currentUser['last_login'])) : 'Nunca'; ?></small>
                                                    </div>
                                                    <span class="badge bg-primary rounded-pill">Info</span>
                                                </div>
                                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <h6 class="my-0">Endereço IP</h6>
                                                        <small class="text-muted"><?php echo htmlspecialchars($currentUser['last_login_ip'] ?: 'N/A'); ?></small>
                                                    </div>
                                                    <span class="badge bg-success rounded-pill">Ativo</span>
                                                </div>
                                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <h6 class="my-0">Função</h6>
                                                        <small class="text-muted"><?php echo htmlspecialchars($currentUser['role_display_name']); ?></small>
                                                    </div>
                                                    <span class="badge bg-info rounded-pill">Admin</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div> <!-- end col -->
                    </div>
                    <!-- end row -->
                
                </div> <!-- container -->

            </div> <!-- content -->

            <!-- Footer Start -->
            <footer class="footer">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-md-6">
                            <script>document.write(new Date().getFullYear())</script> © Santa Casa da Misericórdia da Covilhã
                        </div>
                        <div class="col-md-6">
                            <div class="text-md-end footer-links d-none d-md-block">
                                <a href="javascript: void(0);">Sobre</a>
                                <a href="javascript: void(0);">Suporte</a>
                                <a href="javascript: void(0);">Contacto</a>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
            <!-- end Footer -->

        </div>

        <!-- ============================================================== -->
        <!-- End Page content -->
        <!-- ============================================================== -->

    </div>
    <!-- END wrapper -->

    <!-- Vendor js -->
    <script src="assets/js/vendor.min.js"></script>

    <!-- Daterangepicker js -->
    <script src="assets/vendor/daterangepicker/moment.min.js"></script>
    <script src="assets/vendor/daterangepicker/daterangepicker.js"></script>

    <!-- Apex Charts js -->
    <script src="assets/vendor/apexcharts/apexcharts.min.js"></script>

    <!-- Vector Map js -->
    <script src="assets/vendor/admin-resources/jquery.vectormap/jquery-jvectormap-1.2.2.min.js"></script>
    <script src="assets/vendor/admin-resources/jquery.vectormap/maps/jquery-jvectormap-world-mill-en.js"></script>

    <!-- Dashboard App js -->
    <script src="assets/js/pages/dashboard.js"></script>

    <!-- App js -->
    <script src="assets/js/app.min.js"></script>

    <!-- Custom Admin js -->
    <script src="assets/js/admin.js"></script>

</body>
</html>
