<?php
/**
 * Página de Recuperação de Password
 * Santa Casa da Misericórdia da Covilhã
 */

// Inicializar configuração
require_once 'config/config.php';

// Inicializar middleware de segurança
$security = new SecurityMiddleware();
$security->handle();

// Inicializar gestão de passwords
$passwordManager = new PasswordManager();

$error = '';
$success = '';

// Processar formulário
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitizeInput($_POST['email'] ?? '');
    
    if (empty($email)) {
        $error = 'Por favor, introduza o seu email.';
    } elseif (!isValidEmail($email)) {
        $error = 'Email inválido.';
    } else {
        // Verificar rate limiting
        $security->checkRateLimit('password_reset', 3, 3600); // 3 tentativas por hora
        
        $result = $passwordManager->initiatePasswordReset($email);
        
        if ($result['success']) {
            $success = $result['message'];
        } else {
            $error = $result['message'];
        }
    }
}

$pageTitle = 'Recuperar Password';
?>
<!DOCTYPE html>
<html lang="pt" data-layout="topnav" data-topbar-color="dark">
<head>
    <meta charset="utf-8">
    <title><?php echo $pageTitle; ?> - <?php echo APP_NAME; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="Painel Administrativo da Santa Casa da Misericórdia da Covilhã" name="description">
    <meta content="Santa Casa da Misericórdia da Covilhã" name="author">
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">

    <!-- App favicon -->
    <link rel="shortcut icon" href="assets/images/favicon.ico">

    <!-- Theme Config Js -->
    <script src="assets/js/config.js"></script>

    <!-- App css -->
    <link href="assets/css/app.min.css" rel="stylesheet" type="text/css" id="app-style">

    <!-- Icons css -->
    <link href="assets/css/icons.min.css" rel="stylesheet" type="text/css">

    <!-- Custom CSS -->
    <link href="assets/css/admin.css" rel="stylesheet" type="text/css">
</head>

<body class="authentication-bg position-relative">
    <div class="account-pages pt-2 pt-sm-5 pb-4 pb-sm-5 position-relative">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-xxl-8 col-lg-10">
                    <div class="card overflow-hidden">
                        <div class="row g-0">
                            <div class="col-lg-6 d-none d-lg-block p-2">
                                <img src="assets/images/auth-img.jpg" alt="" class="img-fluid rounded h-100">
                            </div>
                            <div class="col-lg-6">
                                <div class="d-flex flex-column h-100">
                                    <!-- Removed duplicate logo from auth-brand section -->
                                    <div class="p-4 my-auto">
                                        <!-- Official Logo above form -->
                                        <div class="login-logo-container">
                                            <img src="assets/images/logos/logotipo-preto.svg" alt="Santa Casa da Misericórdia da Covilhã" class="img-fluid login-logo-large">
                                        </div>

                                        <h4 class="fs-20">Recuperar Password</h4>

                        <?php if ($error): ?>
                        <div class="alert alert-danger" role="alert">
                            <i class="ri-error-warning-line me-2"></i><?php echo htmlspecialchars($error); ?>
                        </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                        <div class="alert alert-success" role="alert">
                            <i class="ri-check-line me-2"></i><?php echo htmlspecialchars($success); ?>
                        </div>
                        <?php else: ?>

                        <form method="POST" data-validate="true" class="authentication-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                            <div class="mb-4">
                                <label for="email" class="form-label">Email</label>
                                <input
                                    type="email"
                                    id="email"
                                    name="email"
                                    class="form-control"
                                    placeholder="Digite o seu email para receber instruções"
                                    value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                                    required
                                    autofocus
                                >
                            </div>

                            <div class="mb-0 d-grid text-center">
                                <button class="btn btn-primary fw-semibold w-100" type="submit">
                                    <i class="ri-mail-send-line me-2"></i>
                                    <span class="fw-bold">Enviar Instruções</span>
                                </button>
                            </div>
                        </form>

                        <?php endif; ?>

                        <div class="text-center mt-4">
                            <p class="text-muted fs-16 mb-0">
                                <a href="login.php" class="text-dark ms-1 link-offset-3">
                                    <i class="ri-arrow-left-line me-1"></i>Voltar ao Login
                                </a>
                            </p>
                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vendor js -->
    <script src="assets/js/vendor.min.js"></script>

    <!-- App js -->
    <script src="assets/js/app.min.js"></script>

    <!-- Custom JS -->
    <script src="assets/js/admin.js"></script>
</body>
</html>
